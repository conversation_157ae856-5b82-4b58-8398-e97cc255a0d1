# 分销公告模块需求对齐文档

## 项目上下文分析

### 技术栈
- Spring Boot + MyBatis Plus
- 租户隔离架构（TenantEntity）
- XXL-Job定时任务
- RabbitMQ消息队列（延迟消息）
- OSS文件存储系统

### 项目结构
- 开发模块：hengjian-business/zsmall-system
- 租户类型：com.hengjian.common.core.enums.TenantType（Manager、Supplier、Distributor）
- 基础实体：BaseEntity、TenantEntity
- 响应封装：R<T>
- 分页查询：PageQuery、TableDataInfo

### 现有相关功能
- 已有完整的OSS文件管理系统
- 已有RabbitMQ延迟消息机制（参考锁货活动过期功能）
- 已有租户权限控制体系

## 需求理解确认

### 核心功能模块

#### 1. 管理员端功能（Admin）
- **创建公告**：支持富文本内容、附件上传、设置适用对象、过期时间
- **分页查询公告列表**：支持条件筛选、状态筛选
- **删除公告**：逻辑删除
- **查询公告详情**：完整信息展示
- **启用/停用**：状态控制
- **编辑公告**：修改公告信息
- **公告自动到期**：基于RabbitMQ延迟消息实现

#### 2. 分销商端功能（Distributor）
- **登录弹窗显示**：首次登录显示未读公告，点击"我已知道"后不再弹窗
- **聊天窗口功能**：
  - 查询公告消息列表（分页）
  - 查看公告消息详情
  - 删除公告消息（逻辑删除）
  - 公告批量已读/未读操作

#### 3. 供应商端功能（Supplier）
- 功能与分销商端完全一致

### 数据模型分析

#### 表结构设计
1. **announcement**（公告主表）
   - 支持富文本内容
   - 租户类型适用范围
   - 状态控制（启用/禁用）
   - 过期时间和过期状态

2. **announcement_oss**（公告附件表）
   - 关联OSS文件系统
   - 支持多附件

3. **announcement_read**（公告已读表）
   - 租户维度的已读状态
   - 弹窗显示控制

## 边界确认

### 功能范围
- ✅ 后端接口开发
- ✅ 数据库表结构创建
- ✅ 自动过期机制
- ❌ 前端UI交互
- ❌ 与现有SysNotice系统的整合

### 技术约束
- 必须使用现有的租户隔离机制
- 必须遵循现有的代码规范和架构模式
- 文件上传必须使用现有OSS系统
- 延迟消息必须使用现有RabbitMQ机制

### 业务约束
- 公告仅对指定租户类型可见
- 已读状态按租户维度管理
- 弹窗仅在首次登录时显示
- 过期公告自动更新状态

## 疑问澄清

### 已确认问题
1. ✅ 这是全新功能模块，不需要考虑现有公告系统
2. ✅ 开发位置：hengjian-business/zsmall-system模块
3. ✅ 租户类型使用TenantType枚举
4. ✅ 自动过期使用RabbitMQ延迟消息
5. ✅ 仅开发后端接口，不涉及UI

### 已确认决策点
1. **接口权限控制**：使用LoginHelper.getTenantId()和LoginHelper.getTenantType()进行权限验证
2. **消息删除逻辑**：
   - 管理员删除：所有租户不可见（物理删除或全局标记）
   - 分销商/供应商删除：仅对当前租户不可见（租户级软删除）
3. **批量操作范围**：前端传递指定消息ID列表进行批量操作
4. **过期后处理**：公告过期后所有租户不可见，仅更新过期状态
5. **附件权限**：无权限控制，可直接访问

## 详细接口设计

### 管理员端接口（Admin）

#### 1. 创建公告
```
POST /system/announcement
Content-Type: application/json

Request Body:
{
    "title": "公告标题",
    "tenantType": "Distributor", // 适用对象：Manager/Supplier/Distributor
    "content": "富文本内容",
    "expiredTime": "2024-12-31 23:59:59",
    "ossIds": [1, 2, 3] // 附件OSS ID列表
}

Response: R<Void>
```

#### 2. 分页查询公告列表
```
GET /system/announcement/list?pageNum=1&pageSize=10&title=关键词&tenantType=Distributor&status=0

Response: TableDataInfo<AnnouncementVo>
{
    "code": 200,
    "data": {
        "total": 100,
        "rows": [
            {
                "id": 1,
                "title": "公告标题",
                "tenantType": "Distributor",
                "status": 0, // 0启用 1禁用
                "expiredTime": "2024-12-31 23:59:59",
                "isExpired": 0, // 0未过期 1已过期
                "createTime": "2024-01-01 00:00:00"
            }
        ]
    }
}
```

#### 3. 查询公告详情
```
GET /system/announcement/{id}

Response: R<AnnouncementVo>
{
    "code": 200,
    "data": {
        "id": 1,
        "title": "公告标题",
        "tenantType": "Distributor",
        "status": 0,
        "expiredTime": "2024-12-31 23:59:59",
        "isExpired": 0,
        "content": "富文本内容",
        "attachments": [
            {
                "ossId": 1,
                "fileName": "附件1.pdf",
                "fileUrl": "https://oss.example.com/file1.pdf"
            }
        ],
        "createTime": "2024-01-01 00:00:00"
    }
}
```

#### 4. 编辑公告
```
PUT /system/announcement
Content-Type: application/json

Request Body:
{
    "id": 1,
    "title": "修改后的标题",
    "tenantType": "Distributor",
    "content": "修改后的内容",
    "expiredTime": "2024-12-31 23:59:59",
    "ossIds": [1, 2, 3]
}

Response: R<Void>
```

#### 5. 删除公告
```
DELETE /system/announcement/{ids} // 支持批量删除，如：1,2,3

Response: R<Void>
```

#### 6. 启用/停用公告
```
PUT /system/announcement/status
Content-Type: application/json

Request Body:
{
    "id": 1,
    "status": 1 // 0启用 1禁用
}

Response: R<Void>
```

### 分销商端接口（Distributor）

#### 1. 获取弹窗公告
```
GET /system/announcement/popup

Response: R<List<AnnouncementVo>>
// 返回未弹窗显示过的公告列表
```

#### 2. 标记弹窗已读
```
PUT /system/announcement/popup/read
Content-Type: application/json

Request Body:
{
    "announcementIds": [1, 2, 3]
}

Response: R<Void>
```

#### 3. 查询消息列表
```
GET /system/announcement/messages?pageNum=1&pageSize=10&isRead=0

Response: TableDataInfo<AnnouncementMessageVo>
{
    "code": 200,
    "data": {
        "total": 50,
        "rows": [
            {
                "id": 1,
                "title": "公告标题",
                "isRead": 0, // 0未读 1已读
                "createTime": "2024-01-01 00:00:00"
            }
        ]
    }
}
```

#### 4. 查看消息详情
```
GET /system/announcement/message/{id}

Response: R<AnnouncementVo>
// 同管理员端详情接口，但会自动标记为已读
```

#### 5. 删除消息
```
DELETE /system/announcement/message/{id}

Response: R<Void>
// 仅对当前租户不可见
```

#### 6. 批量已读
```
PUT /system/announcement/messages/batch-read
Content-Type: application/json

Request Body:
{
    "announcementIds": [1, 2, 3]
}

Response: R<Void>
```

#### 7. 批量未读
```
PUT /system/announcement/messages/batch-unread
Content-Type: application/json

Request Body:
{
    "announcementIds": [1, 2, 3]
}

Response: R<Void>
```

### 供应商端接口（Supplier）
与分销商端接口完全一致，仅路径前缀不同或通过租户类型区分

### 系统内部接口

#### 自动过期处理
```
// MQ消费者方法，不对外暴露HTTP接口
@RabbitListener(queues = "announcement.expire.queue")
public void handleAnnouncementExpire(Long announcementId)
```

## 验收标准
1. 所有接口功能正常，符合RESTful规范
2. 数据库表结构合理，支持业务需求
3. 租户隔离机制正确实现
4. 自动过期功能正常工作
5. 文件上传和访问功能正常
6. 代码符合项目规范，有适当的注释和日志
