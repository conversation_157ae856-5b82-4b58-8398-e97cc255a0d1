# 分销公告模块共识文档

## 明确的需求描述

### 功能概述
开发一个全新的分销公告模块，支持管理员创建和管理公告，分销商和供应商查看和管理自己的公告消息。

### 核心功能
1. **管理员端**：完整的公告CRUD操作、状态管理、自动过期
2. **分销商/供应商端**：登录弹窗、消息列表管理、已读状态控制
3. **系统功能**：基于RabbitMQ的自动过期机制、OSS附件支持

## 技术实现方案

### 技术栈选择
- **框架**：Spring Boot + MyBatis Plus
- **数据库**：MySQL（使用现有租户隔离机制）
- **消息队列**：RabbitMQ（延迟消息实现自动过期）
- **文件存储**：现有OSS系统
- **权限控制**：基于LoginHelper的租户类型验证

### 架构设计
- **模块位置**：hengjian-business/zsmall-system
- **包结构**：
  - controller: 控制器层
  - service: 业务逻辑层  
  - mapper: 数据访问层
  - domain: 实体类
  - vo/bo/dto: 数据传输对象

### 数据库设计
```sql
-- 公告主表
CREATE TABLE `announcement` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `title` varchar(50) NOT NULL COMMENT '标题',
  `tenant_type` varchar(20) NOT NULL COMMENT '适用对象',
  `status` tinyint(1) NOT NULL COMMENT '状态 启用/禁用 0启用 1禁用',
  `expired_time` datetime NOT NULL COMMENT '过期时间',
  `is_expired` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否过期 0未过期 1已经过期',
  `content` text NOT NULL COMMENT '富文本内容',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告';

-- 公告附件表
CREATE TABLE `announcement_oss` (
  `id` bigint DEFAULT NULL COMMENT '主键id',
  `announcement_id` bigint NOT NULL COMMENT '公告id',
  `oss_id` bigint DEFAULT NULL COMMENT '文件ID',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除 0有效 1删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告附件表';

-- 公告已读表
CREATE TABLE `announcement_read` (
  `id` bigint NOT NULL,
  `announcement_id` bigint NOT NULL COMMENT '公告id',
  `tenant_id` varchar(20) NOT NULL COMMENT '租户id',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读 0未读 1已读',
  `is_show_window` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否弹窗(只弹一次) 0未弹窗 1已弹窗'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告已读';
```

## 技术约束和集成方案

### 权限控制方案
```java
// 租户身份验证
String tenantId = LoginHelper.getTenantId();
if (StrUtil.isEmpty(tenantId)) {
    throw new RuntimeException("请登陆后使用");
}

// 租户类型验证
if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
    throw new RuntimeException("只支持供应商操作");
}
```

### 删除逻辑方案
- **管理员删除**：设置del_flag=1，所有租户不可见
- **分销商/供应商删除**：在announcement_read表中标记删除，仅对当前租户不可见

### 自动过期方案
- 创建公告时发送RabbitMQ延迟消息
- 消息到期时自动更新is_expired=1
- 过期公告对所有租户不可见

### OSS集成方案
- 复用现有OSS上传接口
- 公告创建时关联OSS文件ID
- 附件访问无权限限制

## 任务边界限制

### 包含功能
✅ 完整的后端接口开发
✅ 数据库表结构创建和初始化
✅ 自动过期机制实现
✅ 租户隔离和权限控制
✅ 文件附件功能
✅ 单元测试编写

### 不包含功能
❌ 前端页面开发
❌ UI交互设计
❌ 与现有SysNotice系统整合
❌ 性能优化（除非影响基本功能）

## 验收标准

### 功能验收
1. **管理员端**：
   - 能够创建包含富文本和附件的公告
   - 支持分页查询和条件筛选
   - 可以编辑、删除、启用/停用公告
   - 公告能够按时自动过期

2. **分销商/供应商端**：
   - 登录时能正确显示弹窗公告
   - 消息列表支持分页和已读筛选
   - 查看详情时自动标记已读
   - 支持批量已读/未读操作
   - 删除操作仅对当前租户生效

3. **系统功能**：
   - 租户隔离正确实现
   - 自动过期机制正常工作
   - 附件上传和访问正常

### 技术验收
1. **代码质量**：
   - 遵循项目现有代码规范
   - 适当的注释和日志
   - 异常处理完善

2. **数据一致性**：
   - 事务处理正确
   - 数据库约束合理
   - 并发安全

3. **接口规范**：
   - RESTful API设计
   - 统一的响应格式
   - 适当的HTTP状态码

## 确认的关键假设

1. **租户类型**：使用TenantType枚举（Manager、Supplier、Distributor）
2. **权限验证**：基于LoginHelper进行租户身份和类型验证
3. **删除策略**：软删除，区分管理员删除和用户删除
4. **过期处理**：基于RabbitMQ延迟消息，参考现有锁货活动过期机制
5. **附件处理**：使用现有OSS系统，无额外权限控制
6. **开发范围**：仅后端接口，不涉及前端开发

## 风险评估

### 技术风险
- **低风险**：基于成熟的技术栈和现有架构
- **依赖风险**：依赖现有OSS和MQ系统，需确保稳定性

### 业务风险
- **数据一致性**：多租户环境下的数据隔离需要仔细测试
- **性能风险**：大量公告和租户情况下的查询性能

### 缓解措施
- 充分的单元测试和集成测试
- 分阶段开发和测试
- 参考现有类似功能的实现模式
