# 分销公告模块待办事项

## 必须完成的配置和验证

### 1. RabbitMQ配置验证 🔴 **高优先级**
**问题描述：** 需要确认RabbitMQ延迟消息插件是否已安装和配置

**操作指引：**
```bash
# 1. 检查RabbitMQ延迟消息插件是否安装
rabbitmq-plugins list | grep delayed

# 2. 如果未安装，需要安装延迟消息插件
rabbitmq-plugins enable rabbitmq_delayed_message_exchange

# 3. 重启RabbitMQ服务
systemctl restart rabbitmq-server
```

**验证方法：**
- 启动应用后检查RabbitMQ管理界面
- 确认以下队列和交换机已创建：
  - 队列：`announcement.expire.process.queue`
  - 交换机：`activity.expire.delay.exchange`（类型：x-delayed-message）

### 2. 数据库表创建验证 🔴 **高优先级**
**问题描述：** 确认数据库表是否已正确创建

**操作指引：**
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'announcement%';

-- 检查表结构
DESC announcement;
DESC announcement_oss;
DESC announcement_read;

-- 如果表不存在，执行建表SQL（需要提供完整的建表脚本）
```

**验证方法：**
- 确认三张表都已创建
- 确认字段类型和约束正确
- 确认索引已创建

### 3. 编译测试 🟡 **中优先级**
**问题描述：** 验证代码编译无误

**操作指引：**
```bash
# 在项目根目录执行
mvn clean compile

# 检查编译结果
mvn test-compile
```

**可能遇到的问题：**
- 依赖包缺失：检查pom.xml依赖
- 导入包错误：检查import语句
- 语法错误：根据编译错误信息修复

### 4. 单元测试执行 🟡 **中优先级**
**问题描述：** 执行单元测试验证功能

**操作指引：**
```bash
# 执行特定测试类
mvn test -Dtest=AnnouncementServiceTest

# 执行所有测试
mvn test
```

**注意事项：**
- 测试前需要配置测试数据库
- 可能需要Mock外部依赖
- 确保测试环境配置正确

## 可选的优化和扩展

### 5. 权限控制增强 🟢 **低优先级**
**建议：** 添加更细粒度的权限控制

**操作指引：**
- 在Controller方法上添加`@SaCheckPermission`注解
- 定义具体的权限码，如：`system:announcement:add`
- 在权限管理系统中配置相应权限

### 6. 缓存优化 🟢 **低优先级**
**建议：** 对频繁查询的数据添加缓存

**操作指引：**
- 在Service方法上添加`@Cacheable`注解
- 配置Redis缓存策略
- 注意缓存更新和失效机制

### 7. 监控和日志 🟢 **低优先级**
**建议：** 添加业务监控和详细日志

**操作指引：**
- 添加业务指标监控
- 完善异常日志记录
- 添加性能监控点

## 部署前检查清单

### 环境配置检查
- [ ] RabbitMQ延迟消息插件已安装
- [ ] 数据库表已创建
- [ ] OSS配置正确
- [ ] 应用配置文件正确

### 功能验证检查
- [ ] 管理员端接口测试通过
- [ ] 用户端接口测试通过
- [ ] 延迟消息功能正常
- [ ] 文件上传功能正常
- [ ] 租户隔离功能正常

### 性能和安全检查
- [ ] 数据库查询性能测试
- [ ] 并发访问测试
- [ ] 权限控制验证
- [ ] 数据安全验证

## 常见问题解决方案

### Q1: RabbitMQ连接失败
**解决方案：**
1. 检查RabbitMQ服务是否启动
2. 检查连接配置（地址、端口、用户名、密码）
3. 检查网络连接和防火墙设置

### Q2: 数据库连接失败
**解决方案：**
1. 检查数据库服务是否启动
2. 检查连接配置（地址、端口、数据库名、用户名、密码）
3. 检查数据库用户权限

### Q3: OSS文件上传失败
**解决方案：**
1. 检查OSS配置（AccessKey、SecretKey、Bucket等）
2. 检查网络连接
3. 检查文件大小和格式限制

### Q4: 延迟消息不生效
**解决方案：**
1. 确认RabbitMQ延迟消息插件已安装
2. 检查交换机类型是否为`x-delayed-message`
3. 检查消息头`x-delay`设置是否正确

## 联系支持

如果遇到以上问题无法解决，请提供以下信息：

1. **错误日志**：完整的错误堆栈信息
2. **环境信息**：操作系统、Java版本、数据库版本等
3. **配置信息**：相关配置文件内容（注意隐藏敏感信息）
4. **复现步骤**：详细的操作步骤

**优先处理顺序：**
🔴 高优先级 - 影响基本功能，必须立即解决
🟡 中优先级 - 影响部分功能，建议尽快解决  
🟢 低优先级 - 优化性问题，可以后续处理

---

**最后更新时间：** 2024-12-27
**文档版本：** 1.0
