# 分销公告模块系统架构设计

## 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A1[管理员前端]
        A2[分销商前端]
        A3[供应商前端]
    end
    
    subgraph "控制器层"
        B1[AnnouncementController<br/>管理员端接口]
        B2[AnnouncementMessageController<br/>分销商/供应商端接口]
    end
    
    subgraph "业务逻辑层"
        C1[IAnnouncementService<br/>公告业务接口]
        C2[AnnouncementServiceImpl<br/>公告业务实现]
        C3[IAnnouncementReadService<br/>已读状态业务接口]
        C4[AnnouncementReadServiceImpl<br/>已读状态业务实现]
    end
    
    subgraph "数据访问层"
        D1[AnnouncementMapper<br/>公告数据访问]
        D2[AnnouncementOssMapper<br/>附件关联数据访问]
        D3[AnnouncementReadMapper<br/>已读状态数据访问]
        D4[SysOssMapper<br/>文件数据访问]
    end
    
    subgraph "数据库层"
        E1[(announcement<br/>公告主表)]
        E2[(announcement_oss<br/>公告附件关联表)]
        E3[(announcement_read<br/>公告已读表)]
        E4[(sys_oss<br/>文件存储表)]
    end
    
    subgraph "消息队列"
        F1[RabbitMQ<br/>延迟消息队列]
        F2[AnnouncementExpireHandler<br/>过期处理器]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B2
    
    B1 --> C1
    B2 --> C1
    B1 --> C3
    B2 --> C3
    
    C1 --> C2
    C3 --> C4
    
    C2 --> D1
    C2 --> D2
    C2 --> D4
    C4 --> D3
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E4
    
    C2 --> F1
    F1 --> F2
    F2 --> C2
```

## 分层设计和核心组件

### 1. 控制器层 (Controller Layer)

#### AnnouncementController (管理员端)
- **职责**: 处理管理员的公告管理请求
- **主要方法**:
  - `createAnnouncement()` - 创建公告
  - `getAnnouncementList()` - 分页查询公告列表
  - `getAnnouncementDetail()` - 获取公告详情
  - `updateAnnouncement()` - 更新公告
  - `deleteAnnouncement()` - 删除公告
  - `updateAnnouncementStatus()` - 更新公告状态

#### AnnouncementMessageController (分销商/供应商端)
- **职责**: 处理分销商和供应商的消息查看请求
- **主要方法**:
  - `getPopupAnnouncements()` - 获取弹窗公告
  - `markPopupRead()` - 标记弹窗已读
  - `getMessageList()` - 获取消息列表
  - `getMessageDetail()` - 获取消息详情
  - `deleteMessage()` - 删除消息
  - `batchMarkRead()` - 批量标记已读
  - `batchMarkUnread()` - 批量标记未读

### 2. 业务逻辑层 (Service Layer)

#### IAnnouncementService & AnnouncementServiceImpl
- **职责**: 公告核心业务逻辑
- **主要功能**:
  - 公告CRUD操作
  - 状态管理和过期处理
  - 附件关联管理
  - 延迟消息发送
  - 租户权限验证

#### IAnnouncementReadService & AnnouncementReadServiceImpl
- **职责**: 公告已读状态管理
- **主要功能**:
  - 已读状态CRUD操作
  - 弹窗状态管理
  - 批量操作处理
  - 租户隔离逻辑

### 3. 数据访问层 (Mapper Layer)

#### AnnouncementMapper
- **职责**: 公告主表数据访问
- **继承**: `BaseMapperPlus<Announcement, AnnouncementVo>`

#### AnnouncementOssMapper
- **职责**: 公告附件关联表数据访问
- **继承**: `BaseMapperPlus<AnnouncementOss, AnnouncementOssVo>`

#### AnnouncementReadMapper
- **职责**: 公告已读表数据访问
- **继承**: `BaseMapperPlus<AnnouncementRead, AnnouncementReadVo>`

### 4. 消息处理组件

#### AnnouncementExpireHandler
- **职责**: 处理公告自动过期
- **注解**: `@RabbitListener`
- **功能**: 接收延迟消息，更新公告过期状态

## 模块依赖关系图

```mermaid
graph LR
    subgraph "公告模块"
        A[AnnouncementController]
        B[AnnouncementService]
        C[AnnouncementMapper]
    end
    
    subgraph "已读状态模块"
        D[AnnouncementMessageController]
        E[AnnouncementReadService]
        F[AnnouncementReadMapper]
    end
    
    subgraph "文件模块"
        G[SysOssService]
        H[SysOssMapper]
    end
    
    subgraph "消息队列模块"
        I[RabbitTemplate]
        J[AnnouncementExpireHandler]
    end
    
    subgraph "基础模块"
        K[LoginHelper]
        L[TenantHelper]
        M[BaseEntity]
    end
    
    A --> B
    B --> C
    B --> G
    B --> I
    
    D --> E
    D --> B
    E --> F
    
    J --> B
    
    B --> K
    B --> L
    C --> M
    F --> M
```

## 接口契约定义

### 数据传输对象 (DTO/VO/BO)

#### AnnouncementBo (业务对象)
```java
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Announcement.class, reverseConvertGenerate = false)
public class AnnouncementBo extends BaseEntity {
    private Long id;
    
    @NotBlank(message = "公告标题不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "公告标题不能超过50个字符")
    private String title;
    
    @NotBlank(message = "适用对象不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantType;
    
    private Integer status;
    
    @NotNull(message = "过期时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date expiredTime;
    
    @NotBlank(message = "公告内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;
    
    private List<Long> ossIds; // 附件OSS ID列表
}
```

#### AnnouncementVo (视图对象)
```java
@Data
public class AnnouncementVo {
    private Long id;
    private String title;
    private String tenantType;
    private Integer status;
    private Date expiredTime;
    private Integer isExpired;
    private String content;
    private Date createTime;
    private Date updateTime;
    
    // 附件列表 - 关联sys_oss表
    private List<SysOssVo> attachments;
}
```

#### AnnouncementMessageVo (消息视图对象)
```java
@Data
public class AnnouncementMessageVo {
    private Long id;
    private String title;
    private Integer isRead;
    private Integer isShowWindow;
    private Date createTime;
}
```

### 核心实体类

#### Announcement (公告实体)
```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("announcement")
public class Announcement extends BaseEntity {
    @TableId(value = "id")
    private Long id;
    
    private String title;
    private String tenantType;
    private Integer status;
    private Date expiredTime;
    private Integer isExpired;
    private String content;
    
    @TableLogic
    private Integer delFlag;
}
```

#### AnnouncementOss (公告附件关联实体)
```java
@Data
@TableName("announcement_oss")
public class AnnouncementOss {
    @TableId(value = "id")
    private Long id;
    
    private Long announcementId;
    private Long ossId;
    
    @TableLogic
    private Integer delFlag;
}
```

#### AnnouncementRead (公告已读实体)
```java
@Data
@TableName("announcement_read")
public class AnnouncementRead {
    @TableId(value = "id")
    private Long id;
    
    private Long announcementId;
    private String tenantId;
    private Integer isRead;
    private Integer isShowWindow;
}
```

## 数据流向图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant AC as AnnouncementController
    participant AS as AnnouncementService
    participant AM as AnnouncementMapper
    participant MQ as RabbitMQ
    participant DB as Database
    
    Note over Admin,DB: 创建公告流程
    Admin->>AC: POST /announcement
    AC->>AS: createAnnouncement(bo)
    AS->>AM: insert(announcement)
    AM->>DB: INSERT announcement
    AS->>AM: batchInsert(announcementOss)
    AM->>DB: INSERT announcement_oss
    AS->>MQ: 发送延迟消息
    AS->>AC: 返回结果
    AC->>Admin: R<Void>
    
    Note over MQ,DB: 自动过期流程
    MQ->>AS: 延迟消息到期
    AS->>AM: updateExpiredStatus(id)
    AM->>DB: UPDATE announcement SET is_expired=1
```

## 异常处理策略

### 1. 业务异常处理
- **权限异常**: 租户身份验证失败时抛出RuntimeException
- **数据验证异常**: 使用@Validated注解进行参数校验
- **业务逻辑异常**: 自定义业务异常类，统一异常处理

### 2. 系统异常处理
- **数据库异常**: 事务回滚，记录错误日志
- **消息队列异常**: 重试机制，失败记录
- **文件操作异常**: 优雅降级，不影响主流程

### 3. 异常响应格式
```java
// 统一异常响应
{
    "code": 500,
    "msg": "操作失败：具体错误信息",
    "data": null
}
```

## 设计原则确认

### 1. 单一职责原则
- 每个Service只负责特定的业务领域
- Controller只负责请求处理和响应
- Mapper只负责数据访问

### 2. 依赖倒置原则
- Service层依赖接口而非实现
- 使用Spring的依赖注入管理对象

### 3. 开闭原则
- 通过接口扩展功能
- 配置化的租户类型支持

### 4. 里氏替换原则
- 继承BaseEntity保持一致性
- 实现类可以替换接口

## 与现有系统的集成点

### 1. 租户系统集成
- 使用LoginHelper获取租户信息
- 继承TenantEntity实现租户隔离

### 2. OSS系统集成
- 复用现有SysOssService
- 关联sys_oss表获取文件信息

### 3. 消息队列集成
- 使用现有RabbitTemplate
- 参考活动过期机制实现延迟消息

### 4. 权限系统集成
- 基于租户类型进行权限控制
- 不使用传统的角色权限模式
