# 分销公告模块任务拆分文档

## 任务依赖关系图

```mermaid
graph TD
    T1[T1: 数据库初始化] --> T2[T2: 核心实体类创建]
    T2 --> T3[T3: 数据传输对象创建]
    T3 --> T4[T4: 公告数据访问层]
    T3 --> T5[T5: 已读状态数据访问层]
    T4 --> T6[T6: 公告业务逻辑层]
    T5 --> T7[T7: 已读状态业务逻辑层]
    T6 --> T8[T8: 管理员控制器]
    T6 --> T9[T9: 分销商/供应商控制器]
    T7 --> T9
    T6 --> T10[T10: 消息队列处理器]
    T8 --> T11[T11: 单元测试]
    T9 --> T11
    T10 --> T11
```

## 原子任务详细定义

### T1: 数据库初始化
**输入契约:**
- 数据库表结构设计文档
- 现有数据库连接配置

**输出契约:**
- 创建完成的数据库表：announcement、announcement_oss、announcement_read
- 表结构符合设计规范，包含所有必要字段和索引

**实现约束:**
- 使用MySQL数据库
- 遵循项目现有的表命名和字段命名规范
- 添加必要的索引优化查询性能

**验收标准:**
- SQL脚本能够成功执行
- 表结构与设计文档完全一致
- 主键、外键、索引创建正确

---

### T2: 核心实体类创建
**输入契约:**
- 已创建的数据库表结构
- 项目现有实体类规范和基类

**输出契约:**
- Announcement实体类（继承BaseEntity）
- AnnouncementOss实体类
- AnnouncementRead实体类
- 所有实体类包含正确的MyBatis Plus注解

**实现约束:**
- 继承项目基类（BaseEntity）
- 使用MyBatis Plus注解进行ORM映射
- 遵循项目代码规范和命名约定

**验收标准:**
- 实体类字段与数据库表字段完全对应
- 注解使用正确（@TableName、@TableId、@TableLogic等）
- 代码符合项目规范，有适当注释

---

### T3: 数据传输对象创建
**输入契约:**
- 核心实体类
- 接口设计文档中的请求/响应格式

**输出契约:**
- AnnouncementBo（业务对象，用于接收前端请求）
- AnnouncementVo（视图对象，用于返回详情数据）
- AnnouncementMessageVo（消息视图对象，用于消息列表）
- AnnouncementOssVo（附件视图对象）
- AnnouncementReadVo（已读状态视图对象）

**实现约束:**
- 使用项目统一的验证注解
- 配置AutoMapper进行对象转换
- 继承适当的基类

**验收标准:**
- DTO字段完整，覆盖所有业务场景
- 验证注解配置正确
- AutoMapper配置无误，能够正确转换

---

### T4: 公告数据访问层
**输入契约:**
- Announcement、AnnouncementOss实体类
- 相关VO类

**输出契约:**
- AnnouncementMapper接口（继承BaseMapperPlus）
- AnnouncementOssMapper接口
- 对应的XML映射文件
- 自定义查询方法

**实现约束:**
- 继承BaseMapperPlus获得基础CRUD功能
- 实现分页查询、条件查询等自定义方法
- 支持租户隔离（如果需要）

**验收标准:**
- 基础CRUD操作可用
- 自定义查询方法正确实现
- XML文件语法正确，查询逻辑无误

---

### T5: 已读状态数据访问层
**输入契约:**
- AnnouncementRead实体类
- AnnouncementReadVo类

**输出契约:**
- AnnouncementReadMapper接口
- 对应的XML映射文件
- 支持租户隔离的查询和更新方法

**实现约束:**
- 实现租户维度的数据隔离
- 支持批量操作
- 优化查询性能

**验收标准:**
- 租户隔离查询正确
- 批量操作方法可用
- 查询性能满足要求

---

### T6: 公告业务逻辑层
**输入契约:**
- AnnouncementMapper、AnnouncementOssMapper接口
- 相关DTO类
- OSS服务接口
- RabbitTemplate

**输出契约:**
- IAnnouncementService业务接口
- AnnouncementServiceImpl业务实现类
- 完整的公告管理功能

**实现约束:**
- 实现租户权限验证
- 集成OSS文件管理
- 实现延迟消息发送
- 事务管理和异常处理

**验收标准:**
- 所有CRUD操作功能完整
- 权限验证正确
- 文件关联功能正常
- 延迟消息发送成功

---

### T7: 已读状态业务逻辑层
**输入契约:**
- AnnouncementReadMapper接口
- 相关DTO类

**输出契约:**
- IAnnouncementReadService业务接口
- AnnouncementReadServiceImpl业务实现类

**实现约束:**
- 实现租户隔离逻辑
- 支持批量操作
- 弹窗状态管理

**验收标准:**
- 已读状态管理功能完整
- 租户隔离正确
- 批量操作性能良好

---

### T8: 管理员控制器
**输入契约:**
- IAnnouncementService业务接口

**输出契约:**
- AnnouncementController控制器类
- 完整的管理员端REST API

**实现约束:**
- 遵循RESTful API设计规范
- 使用统一的响应格式
- 实现参数验证和异常处理

**验收标准:**
- 所有管理员端接口可用
- 参数验证正确
- 响应格式统一
- 异常处理完善

---

### T9: 分销商/供应商控制器
**输入契约:**
- IAnnouncementService、IAnnouncementReadService业务接口

**输出契约:**
- AnnouncementMessageController控制器类
- 完整的用户端REST API

**实现约束:**
- 实现租户类型权限验证
- 支持分销商和供应商两种租户类型
- 统一的接口设计

**验收标准:**
- 所有用户端接口可用
- 租户权限验证正确
- 接口功能完整

---

### T10: 消息队列处理器
**输入契约:**
- IAnnouncementService业务接口
- RabbitMQ配置

**输出契约:**
- AnnouncementExpireHandler消息处理器
- 自动过期功能

**实现约束:**
- 使用@RabbitListener注解
- 实现幂等性处理
- 异常处理和重试机制

**验收标准:**
- 能够正确接收延迟消息
- 过期状态更新正确
- 异常情况处理得当

---

### T11: 单元测试
**输入契约:**
- 所有业务类和控制器类

**输出契约:**
- 完整的单元测试套件
- 测试覆盖核心业务逻辑

**实现约束:**
- 使用JUnit和Mockito
- 测试用例覆盖正常和异常情况
- Mock外部依赖

**验收标准:**
- 测试覆盖率达到80%以上
- 所有核心功能测试通过
- 测试用例设计合理

## 任务执行顺序

**阶段1: 基础设施（T1-T3）**
1. T1: 数据库初始化
2. T2: 核心实体类创建  
3. T3: 数据传输对象创建

**阶段2: 数据访问层（T4-T5）**
4. T4: 公告数据访问层
5. T5: 已读状态数据访问层

**阶段3: 业务逻辑层（T6-T7）**
6. T6: 公告业务逻辑层
7. T7: 已读状态业务逻辑层

**阶段4: 控制器层（T8-T9）**
8. T8: 管理员控制器
9. T9: 分销商/供应商控制器

**阶段5: 系统集成（T10-T11）**
10. T10: 消息队列处理器
11. T11: 单元测试

## 风险评估

**技术风险:**
- 低风险：基于成熟框架和现有架构
- 依赖风险：需要确保OSS和MQ服务可用

**复杂度评估:**
- 每个任务预计20-30分钟完成
- 总体复杂度中等，适合分步实施

**质量保证:**
- 每个任务完成后立即验证
- 阶段性集成测试
- 最终端到端测试
