# 表名和类名修改说明：order_es_processing_failed → canal_processing_failed

## 概述
将数据库表 `order_es_processing_failed` 改名为 `canal_processing_failed`，同时将相关类名从 `OrderEsProcessingFailed` 系列改名为 `CanalProcessingFailed` 系列，使命名更加统一和语义化。

## 修改内容

### 1. 实体类重命名
**原文件**: `OrderEsProcessingFailed.java` → **新文件**: `CanalProcessingFailed.java`

修改内容：
- 类名：`OrderEsProcessingFailed` → `CanalProcessingFailed`
- 更新 `@TableName` 注解：`"order_es_processing_failed"` → `"canal_processing_failed"`
- 更新类注释：`订单ES消息处理失败对象` → `Canal消息处理失败对象`

### 2. 视图对象重命名
**原文件**: `OrderEsProcessingFailedVo.java` → **新文件**: `CanalProcessingFailedVo.java`

修改内容：
- 类名：`OrderEsProcessingFailedVo` → `CanalProcessingFailedVo`
- 更新类注释：`订单ES消息处理失败视图对象` → `Canal消息处理失败视图对象`
- 更新 `@AutoMapper` 注解目标类：`OrderEsProcessingFailed.class` → `CanalProcessingFailed.class`

### 3. Mapper接口重命名
**原文件**: `OrderEsProcessingFailedMapper.java` → **新文件**: `CanalProcessingFailedMapper.java`

修改内容：
- 接口名：`OrderEsProcessingFailedMapper` → `CanalProcessingFailedMapper`
- 更新接口注释：`订单ES消息处理失败Mapper接口` → `Canal消息处理失败Mapper接口`
- 更新泛型参数：`BaseMapperPlus<OrderEsProcessingFailed, OrderEsProcessingFailedVo>` → `BaseMapperPlus<CanalProcessingFailed, CanalProcessingFailedVo>`

### 4. Mapper XML重命名
**原文件**: `OrderEsProcessingFailedMapper.xml` → **新文件**: `CanalProcessingFailedMapper.xml`

修改内容：
- 更新 namespace：`com.zsmall.order.entity.mapper.OrderEsProcessingFailedMapper` → `com.zsmall.order.entity.mapper.CanalProcessingFailedMapper`

### 5. 业务代码引用更新

#### CanalOrderJob.java
**文件**: `hengjian-business/zsmall-order/zsmall-order-biz/src/main/java/com/zsmall/order/biz/job/orderEs/CanalOrderJob.java`

修改内容：
- 导入类：`OrderEsProcessingFailed` → `CanalProcessingFailed`
- 导入Mapper：`OrderEsProcessingFailedMapper` → `CanalProcessingFailedMapper`
- 变量名：`orderEsProcessingFailedMapper` → `canalProcessingFailedMapper`
- 变量名：`orderEsProcessingFailedList` → `canalProcessingFailedList`
- 注释：`查询订单ES消息处理失败表` → `查询Canal消息处理失败表`
- 日志：`orderEsProcessingFailed` → `canalProcessingFailed`

#### CanalOrderRabbitMQListener.java
**文件**: `hengjian-business/zsmall-order/zsmall-order-biz/src/main/java/com/zsmall/order/biz/mq/CanalOrderRabbitMQListener.java`

修改内容：
- 导入类：`OrderEsProcessingFailed` → `CanalProcessingFailed`
- 导入Mapper：`OrderEsProcessingFailedMapper` → `CanalProcessingFailedMapper`
- 变量名：`orderEsProcessingFailedMapper` → `canalProcessingFailedMapper`
- 对象创建：`new OrderEsProcessingFailed()` → `new CanalProcessingFailed()`
- 注释：`记录进ES异常表` → `记录进Canal处理失败表`

#### CanalProductSkuStockRabbitMQListener.java
**文件**: `hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/mq/CanalProductSkuStockRabbitMQListener.java`

修改内容：
- 导入类：`OrderEsProcessingFailed` → `CanalProcessingFailed`
- 导入Mapper：`OrderEsProcessingFailedMapper` → `CanalProcessingFailedMapper`
- 变量名：`orderEsProcessingFailedMapper` → `canalProcessingFailedMapper`
- 对象创建：`new OrderEsProcessingFailed()` → `new CanalProcessingFailed()`
- 注释：`记录进ES异常表` → `记录进Canal处理失败表`

### 6. 文档更新
**文件**: `docs/CanalProductSkuStockRabbitMQListener_创建说明.md`

修改内容：
- 更新异常处理机制说明：从 `OrderEsProcessingFailedMapper` 改为 `CanalProcessingFailedMapper`
- 更新表名引用：`order_es_processing_failed` → `canal_processing_failed`

## 数据库变更

需要执行以下SQL语句来重命名数据库表：

```sql
-- 重命名表
ALTER TABLE order_es_processing_failed RENAME TO canal_processing_failed;
```

## 影响范围

### 保持不变的内容
- ✅ 字段结构完全不变
- ✅ 业务逻辑完全不变
- ✅ 数据库操作方式完全不变

### 变更的内容
- 数据库表名：`order_es_processing_failed` → `canal_processing_failed`
- 实体类名：`OrderEsProcessingFailed` → `CanalProcessingFailed`
- 视图对象类名：`OrderEsProcessingFailedVo` → `CanalProcessingFailedVo`
- Mapper接口名：`OrderEsProcessingFailedMapper` → `CanalProcessingFailedMapper`
- 相关注释和文档说明更新为更通用的Canal处理失败表

## 兼容性说明

1. **重大变更**：类名发生了变化，需要重新编译和部署应用程序
2. **数据迁移**：通过重命名表的方式，现有数据完全保留
3. **功能扩展**：新的命名更好地反映了其通用性，可以用于记录所有Canal相关的处理失败消息
4. **代码更新**：所有引用旧类名的地方都已更新为新的类名

## 注意事项

1. 在生产环境执行表重命名操作前，请确保：
   - 备份相关数据
   - 在维护窗口期间执行
   - 验证应用程序连接正常

2. 如果有其他系统或脚本直接引用了旧表名，需要同步更新

3. 监控和日志系统中如果有硬编码的表名，也需要相应更新
