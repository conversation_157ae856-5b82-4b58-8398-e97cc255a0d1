# 富文本XSS过滤问题修复说明

## 问题描述

用户在创建公告时，富文本内容中的HTML标签和样式被移除，导致接收到的内容变成纯文本。

**原始请求内容：**
```json
{
    "title": "测试公告",
    "tenantType": "Distributor",
    "status": 0,
    "expiredTime": "2025-08-27 16:29:14",
    "isExpired": 0,
    "content": "<p style=\"text-align: center;\"><strong>测试公告</strong></p><p><strong>这是一条测试公告</strong><strong><span style=\"background-color: rgb(192, 80, 77);\">，<span style=\"background-color: rgb(227, 108, 9);\">作用范围是供应商</span>/<span style=\"background-color: rgb(149, 55, 52);\">分销商</span>/<span style=\"background-color: rgb(75, 172, 198);\">超管</span></span></strong></p>",
    "ossIds": [1780157865738985474, 1780186565327032321]
}
```

**实际接收到的内容：**
```
测试公告这是一条测试公告，作用范围是供应商/分销商/超管nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; nbsp; 2025年8月27号
```

## 问题根因

XSS过滤器 `XssHttpServletRequestWrapper` 对所有匹配 `/system/*` 路径的JSON请求进行过滤，使用 `HtmlUtil.cleanHtmlTag()` 方法清除所有HTML标签，导致富文本内容被处理成纯文本。

### 相关代码位置

1. **过滤器配置：** `hengjian-common/hengjian-common-web/src/main/java/com/hengjian/common/web/config/FilterConfig.java`
2. **过滤器实现：** `hengjian-common/hengjian-common-web/src/main/java/com/hengjian/common/web/filter/XssHttpServletRequestWrapper.java`
3. **配置文件：** `application.yml` 中的 `xss.excludes` 配置

### 过滤逻辑

```java
private static String escapeValue(String values) {
    String valueString = HtmlUtil.cleanHtmlTag(values); // 清除HTML标签
    
    Pattern pattern = Pattern.compile(SPECIAL_CHARACTERS, Pattern.CASE_INSENSITIVE);
    Matcher matchered = pattern.matcher(valueString);
    valueString = matchered.replaceAll("");
    
    return valueString.trim();
}
```

## 解决方案

将公告接口路径 `/system/announcement` 添加到XSS过滤器的排除列表中。

### 修改的配置文件

1. **hengjian-admin/src/main/resources/application.yml**
2. **hengjian-business/zsmall-xxl-job/src/main/resources/application.yml**

### 修改内容

```yaml
# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/blog/saveBlogArticle,/system/config,/system/localeMessages/add,/system/announcement
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/blog/*,/mp/*
```

## 验证方法

1. 重启应用服务
2. 使用相同的富文本内容创建公告
3. 验证接收到的content字段是否保留了HTML标签和样式

## 安全考虑

虽然排除了公告接口的XSS过滤，但需要注意：

1. **前端验证：** 前端应该对富文本内容进行适当的验证和清理
2. **输出转义：** 在显示富文本内容时，应该使用安全的HTML渲染方式
3. **权限控制：** 确保只有授权用户才能创建和修改公告
4. **内容审核：** 可以考虑添加内容审核机制

## 其他解决方案（备选）

如果不希望完全排除XSS过滤，可以考虑以下方案：

1. **字段级别过滤：** 修改XSS过滤器，对特定字段（如content）使用更宽松的过滤策略
2. **使用HTML清理库：** 使用如OWASP Java HTML Sanitizer等库，允许安全的HTML标签
3. **自定义注解：** 为富文本字段添加自定义注解，跳过XSS过滤

## 测试建议

建议创建单元测试验证：
1. 富文本内容的正确保存和读取
2. XSS攻击向量的防护（确保真正的恶意脚本仍被阻止）
3. 其他系统接口的XSS过滤功能正常工作
