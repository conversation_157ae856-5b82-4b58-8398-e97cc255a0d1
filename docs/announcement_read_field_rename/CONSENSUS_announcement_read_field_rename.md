# CONSENSUS - announcement_read字段重命名任务

## 明确的需求描述
将 `announcement_read` 表中的 `isDeleted` 字段重命名为 `del_flag`，包括：
1. 数据库字段：`is_deleted` → `del_flag`
2. Java实体字段：`isDeleted` → `delFlag`
3. 所有相关代码引用的适配

## 技术实现方案
### 1. 实体类修改
- 修改 `AnnouncementRead.java` 中的字段名
- 添加 `@TableLogic` 注解（与项目规范一致）
- 保持字段注释和类型不变

### 2. 代码引用修改
- 修改 `AnnouncementServiceImpl.java` 中的字段引用
- 修改 `AnnouncementMapper.xml` 中的SQL字段引用
- 确保所有 getter/setter 方法名正确

### 3. 数据库结构修改
- 提供 ALTER TABLE 语句重命名字段
- 保持数据完整性

## 技术约束
- 使用MyBatis-Plus框架
- 遵循项目现有命名规范
- 保持API接口不变
- 保持业务逻辑不变

## 集成方案
- 与现有的软删除机制集成
- 与租户隔离机制保持一致
- 保持事务完整性

## 任务边界限制
- 仅修改 `announcement_read` 相关代码
- 不影响其他表或模块
- 不修改业务逻辑

## 验收标准
1. 所有代码编译通过
2. 字段引用全部更新完成
3. 数据库字段成功重命名
4. 现有功能正常运行
5. 软删除功能正常工作

## 确认事项
- 数据库字段重命名需要提供迁移脚本
- 现有数据需要保留
- 代码修改后需要测试验证
- 遵循先代码后数据库的修改顺序
