# ALIGNMENT - announcement_read字段重命名任务

## 项目上下文分析

### 项目结构
- 项目：hengjian-distribution (恒建分销系统)
- 模块：zsmall-system (公告系统模块)
- 技术栈：Spring Boot + MyBatis-Plus + MySQL
- 架构模式：分层架构 (Controller -> Service -> Mapper -> Entity)

### 现有代码模式
- 使用MyBatis-Plus的BaseMapperPlus
- 实体类使用@TableName、@TableId、@TableLogic注解
- 删除标志统一使用delFlag字段名，配合@TableLogic注解
- VO类不包含删除标志字段
- 使用事务管理(@Transactional)

### 业务域理解
- announcement_read表：公告已读记录表
- 支持租户级删除：通过删除标志实现软删除，仅对当前租户不可见
- 字段含义：is_deleted/isDeleted表示是否删除(租户级) 0未删除 1已删除

## 原始需求
将announcement_read数据库表中的isDeleted字段改为del_flag

## 需求理解
1. **字段重命名**：将实体类中的isDeleted字段改为delFlag
2. **数据库字段重命名**：将数据库表中的is_deleted字段改为del_flag
3. **代码同步修改**：所有使用isDeleted的Java代码改为delFlag
4. **SQL映射修改**：XML文件中的is_deleted改为del_flag
5. **注解添加**：为delFlag字段添加@TableLogic注解，与项目规范保持一致

## 边界确认
- **范围限定**：仅修改announcement_read表相关代码
- **不影响其他表**：不修改announcement表或其他相关表
- **保持业务逻辑不变**：仅字段名修改，业务逻辑保持一致
- **数据兼容性**：需要考虑数据库迁移脚本

## 现有项目理解
通过代码分析发现：
1. AnnouncementRead实体类当前使用isDeleted字段
2. AnnouncementServiceImpl中有设置isDeleted的代码
3. AnnouncementMapper.xml中查询使用is_deleted字段
4. Announcement实体类已经使用delFlag + @TableLogic的标准模式
5. 项目中删除标志的标准命名是delFlag

## 疑问澄清
1. **数据库迁移**：是否需要提供ALTER TABLE语句来重命名数据库字段？
2. **数据保留**：现有数据是否需要保留（应该需要）？
3. **测试范围**：是否需要更新相关的单元测试？
4. **部署顺序**：代码修改和数据库修改的部署顺序？

## 技术约束
- 必须保持与现有MyBatis-Plus框架的兼容性
- 必须保持事务完整性
- 必须遵循项目现有的命名规范
- 必须保持API接口不变
