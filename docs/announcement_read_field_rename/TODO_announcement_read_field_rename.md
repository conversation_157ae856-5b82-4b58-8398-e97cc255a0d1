# TODO - announcement_read字段重命名待办事项

## 🔥 必须执行的操作

### 1. 数据库字段重命名 (高优先级)
**操作**: 执行数据库迁移脚本
```sql
-- 备份表（推荐）
CREATE TABLE announcement_read_backup AS SELECT * FROM announcement_read;

-- 执行字段重命名
ALTER TABLE announcement_read 
RENAME COLUMN is_deleted TO del_flag;
```

**位置**: `docs/announcement_read_field_rename/database_migration.sql`
**时机**: 代码部署后立即执行
**风险**: 高 - 必须在维护窗口执行

### 2. 应用重启 (中优先级)
**操作**: 重启应用服务
**原因**: 刷新数据库连接池，确保新字段名生效
**时机**: 数据库迁移完成后

## ⚠️ 验证检查项

### 1. 功能验证
- [ ] 公告列表查询正常
- [ ] 公告详情查看正常  
- [ ] 租户级删除功能正常
- [ ] 批量标记已读/未读功能正常
- [ ] 公告过期处理正常

### 2. 数据完整性验证
```sql
-- 验证字段存在
SHOW COLUMNS FROM announcement_read LIKE '%del_flag%';

-- 验证数据完整性
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN del_flag = 0 THEN 1 ELSE 0 END) as active_records,
    SUM(CASE WHEN del_flag = 1 THEN 1 ELSE 0 END) as deleted_records
FROM announcement_read;
```

### 3. 关联查询验证
```sql
-- 验证与公告表的关联查询
SELECT COUNT(*) 
FROM announcement a 
INNER JOIN announcement_read ar ON a.id = ar.announcement_id 
WHERE a.del_flag = 0 AND ar.del_flag = 0;
```

## 🛠️ 配置和环境

### 1. 数据库版本检查
**检查命令**: `SELECT VERSION();`
- MySQL 8.0+: 使用 `RENAME COLUMN` 语法
- MySQL 5.7及以下: 使用 `CHANGE COLUMN` 语法

### 2. 连接池配置
**可能需要调整**: 数据库连接池超时设置
**原因**: 确保连接池能及时获取新的字段信息

## 📋 回滚准备

### 1. 回滚脚本准备
**位置**: `docs/announcement_read_field_rename/database_migration.sql` (文件末尾)
```sql
-- 回滚命令
ALTER TABLE announcement_read 
RENAME COLUMN del_flag TO is_deleted;
```

### 2. 代码回滚
**Git提交**: 记录本次修改的commit hash，便于必要时回滚

## 🔍 监控要点

### 1. 应用日志监控
- 关注公告相关功能的错误日志
- 监控数据库连接异常
- 检查MyBatis映射异常

### 2. 数据库监控
- 监控 `announcement_read` 表的查询性能
- 检查是否有字段不存在的错误

## 📞 支持联系

### 技术支持
- **数据库问题**: 联系DBA团队
- **应用问题**: 联系开发团队
- **部署问题**: 联系运维团队

### 紧急情况处理
1. 立即停止相关功能
2. 执行回滚脚本
3. 重启应用服务
4. 验证功能恢复

## ✅ 完成确认

执行完成后，请确认以下检查项：
- [ ] 数据库字段重命名成功
- [ ] 应用启动正常，无错误日志
- [ ] 公告功能测试通过
- [ ] 租户级删除功能正常
- [ ] 数据完整性验证通过
- [ ] 性能无明显影响

**完成标志**: 所有检查项通过，功能正常运行24小时无异常
