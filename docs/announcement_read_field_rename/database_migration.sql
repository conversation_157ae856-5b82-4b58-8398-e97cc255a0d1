-- =====================================================
-- announcement_read 表字段重命名迁移脚本
-- 将 is_deleted 字段重命名为 del_flag
-- 执行日期: 2025-08-27
-- =====================================================

-- 1. 备份建议
-- 在执行此脚本前，建议先备份 announcement_read 表：
-- CREATE TABLE announcement_read_backup AS SELECT * FROM announcement_read;

-- 2. 检查当前表结构
-- DESCRIBE announcement_read;

-- 3. 执行字段重命名（正向迁移）
-- 注意：MySQL 5.7+ 支持 RENAME COLUMN 语法
-- 如果是更早版本，需要使用 CHANGE COLUMN 语法

-- 方式1: MySQL 8.0+ 推荐语法
ALTER TABLE announcement_read
RENAME COLUMN is_deleted TO del_flag;

-- 方式2: MySQL 5.7 及更早版本兼容语法（如果上面的语法不支持，使用这个）
-- ALTER TABLE announcement_read
-- CHANGE COLUMN is_deleted del_flag INT(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）';

-- 4. 验证字段重命名结果
-- DESCRIBE announcement_read;
-- SELECT COUNT(*) FROM announcement_read WHERE del_flag = 0;
-- SELECT COUNT(*) FROM announcement_read WHERE del_flag = 1;

-- =====================================================
-- 回滚脚本（如果需要回滚到原来的字段名）
-- =====================================================

-- 回滚方式1: MySQL 8.0+ 语法
-- ALTER TABLE announcement_read
-- RENAME COLUMN del_flag TO is_deleted;

-- 回滚方式2: MySQL 5.7 及更早版本语法
-- ALTER TABLE announcement_read
-- CHANGE COLUMN del_flag is_deleted INT(1) DEFAULT 0 COMMENT '是否删除(租户级) 0未删除 1已删除';

-- =====================================================
-- 执行后验证脚本
-- =====================================================

-- 验证表结构
-- SHOW COLUMNS FROM announcement_read LIKE '%del_flag%';

-- 验证数据完整性
-- SELECT
--     COUNT(*) as total_records,
--     SUM(CASE WHEN del_flag = 0 THEN 1 ELSE 0 END) as active_records,
--     SUM(CASE WHEN del_flag = 1 THEN 1 ELSE 0 END) as deleted_records
-- FROM announcement_read;

-- 验证与公告表的关联查询
-- SELECT COUNT(*)
-- FROM announcement a
-- INNER JOIN announcement_read ar ON a.id = ar.announcement_id
-- WHERE a.del_flag = 0 AND ar.del_flag = 0;

-- =====================================================
-- 注意事项
-- =====================================================
-- 1. 执行前请确保已经部署了相应的代码修改
-- 2. 建议在维护窗口期间执行
-- 3. 执行前请备份相关表数据
-- 4. 执行后请验证应用程序功能正常
-- 5. 如果使用了数据库连接池，可能需要重启应用以刷新连接
