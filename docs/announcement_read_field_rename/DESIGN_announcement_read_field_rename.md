# DESIGN - announcement_read字段重命名设计

## 整体架构图

```mermaid
graph TB
    A[数据库表 announcement_read] --> B[实体类 AnnouncementRead]
    B --> C[Mapper AnnouncementReadMapper]
    C --> D[Service AnnouncementServiceImpl]
    D --> E[Controller AnnouncementController]
    
    F[XML映射 AnnouncementReadMapper.xml] --> C
    G[VO类 AnnouncementReadVo] --> B
    
    style A fill:#ffcccc
    style B fill:#ffcccc
    style F fill:#ffcccc
    style D fill:#ffcccc
```

## 分层设计和核心组件

### 1. 数据层 (Data Layer)
- **数据库表**: `announcement_read`
  - 字段修改: `is_deleted` → `del_flag`
  - 数据类型: INT(1)
  - 默认值: 0

### 2. 实体层 (Entity Layer)
- **AnnouncementRead.java**
  - 字段修改: `isDeleted` → `delFlag`
  - 添加注解: `@TableLogic`
  - 保持注释和访问器方法

### 3. 持久层 (Persistence Layer)
- **AnnouncementReadMapper.xml**
  - SQL字段引用: `is_deleted` → `del_flag`
  - 影响的查询: `selectByAnnouncementAndTenant`

### 4. 业务层 (Service Layer)
- **AnnouncementServiceImpl.java**
  - 字段引用: `setIsDeleted()` → `setDelFlag()`
  - 影响的方法: `deleteTenantAnnouncements()`

## 模块依赖关系图

```mermaid
graph LR
    A[AnnouncementRead Entity] --> B[AnnouncementReadMapper]
    B --> C[AnnouncementServiceImpl]
    C --> D[AnnouncementController]
    
    E[AnnouncementReadMapper.xml] --> B
    F[Database Table] --> A
    
    style A fill:#ffffcc
    style E fill:#ffffcc
    style F fill:#ffffcc
```

## 接口契约定义

### 实体类接口
```java
// 修改前
private Integer isDeleted;
public Integer getIsDeleted() { ... }
public void setIsDeleted(Integer isDeleted) { ... }

// 修改后
@TableLogic
private Integer delFlag;
public Integer getDelFlag() { ... }
public void setDelFlag(Integer delFlag) { ... }
```

### 数据库接口
```sql
-- 修改前
SELECT is_deleted FROM announcement_read

-- 修改后  
SELECT del_flag FROM announcement_read
```

## 数据流向图

```mermaid
sequenceDiagram
    participant C as Controller
    participant S as Service
    participant M as Mapper
    participant DB as Database
    
    C->>S: deleteTenantAnnouncements()
    S->>S: readRecord.setDelFlag(1)
    S->>M: updateById(readRecord)
    M->>DB: UPDATE announcement_read SET del_flag=1
```

## 异常处理策略

### 1. 编译时异常
- 字段名不匹配导致的编译错误
- 方法名不存在导致的调用错误

### 2. 运行时异常
- 数据库字段不存在异常
- MyBatis映射异常

### 3. 处理方案
- 分步骤修改，确保每步都能编译通过
- 提供数据库迁移脚本
- 保持向后兼容性

## 设计原则

### 1. 最小影响原则
- 仅修改必要的文件
- 保持API接口不变
- 保持业务逻辑不变

### 2. 一致性原则
- 与项目现有的delFlag命名规范一致
- 与@TableLogic注解使用规范一致

### 3. 安全性原则
- 提供数据库备份建议
- 分步骤执行，可回滚
- 保持数据完整性
