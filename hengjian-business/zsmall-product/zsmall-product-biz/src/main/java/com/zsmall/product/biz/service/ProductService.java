package com.zsmall.product.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.bo.product.*;
import com.zsmall.product.entity.domain.vo.ProductRelatedDataCopyVo;
import com.zsmall.product.entity.domain.vo.product.*;
import com.zsmall.product.entity.domain.vo.productImport.ProductImportRecordVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商品SPUService接口
 *
 * <AUTHOR> <PERSON>
 * @date 2023-05-26
 */
public interface ProductService {

    /**
     * 查询商品SPU列表
     */
    ProductTableInfoVo queryPageList(ProductQueryBo bo, PageQuery pageQuery);

    /**
     * 查询商品完整信息
     * @param bo
     * @return
     */
    ProductIntactInfoVo queryIntactInfo(ProductBo bo) throws RStatusCodeException;

    /**
     * 新增商品（完整信息）
     */
    ProductSaveVo insertByIntactInfo(ProductIntactInfoBo bo) throws RStatusCodeException;

    /**
     * 修改商品（完整信息）
     * @param bo
     * @return
     */
    ProductSaveVo updateByIntactInfo(ProductIntactInfoUpdateBo bo) throws RStatusCodeException;

    /**
     * 商品上架/下架
     * @param bo
     * @return
     */
    R<Void> onOrOffShelfProduct(ProductShelfBo bo) throws RStatusCodeException;

    /**
     * 删除商品
     * @param productDeleteBo
     * @return
     * @throws RStatusCodeException
     */
    Boolean deleteProduct(ProductDeleteBo productDeleteBo) throws Exception;

    /**
     * 删除指定渠道商品
     * @param channelId
     * @return
     */
    boolean deleteProductByChannelId(Long channelId);


    /**
     * 翻页查询商品价格修改列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<ProductPriceVo> getProductPriceChange(ProductPriceBo bo, PageQuery pageQuery);

    /**
     * 修改价格
     * @param bo
     * @return
     */
    R<Void> changeProductPrice(PriceFormListBySupBo bo) throws RStatusCodeException;

    /**
     * 分页查询商品导入记录
     * @param pageQuery
     * @return
     */
    TableDataInfo<ProductImportRecordVo> queryProductImportRecordPage(PageQuery pageQuery);

    /**
     * 功能描述：
     * 上传商品Excel,不可用
     *
     * @param file
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2025/04/29
     */
    R<Void> uploadProductExcel(MultipartFile file) throws Exception;
    R<Void> uploadProductExcelNotAsync(MultipartFile file) throws Exception;

    /**
     * 获取商品SKU简单信息
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<ProductSkuSimpleVo> getProductSkuPage(ProductPriceBo bo, PageQuery pageQuery);

    /**
     * 获取自定义导出商品信息可选字段（管理员）
     */
    R<List<CustomExportFieldVo>> getCustomExportFields();

    /**
     * 自定义导出商品信息（管理员）
     */
    R<Void> customExport(CustomExportBo bo);

    /**
     * 功能描述：导出  临时方案
     *
     * @param bo        博
     * @param pageQuery 页面查询
     * @param response  响应
     * <AUTHOR>
     * @date 2024/02/20
     */
    void export(ProductQueryBo bo, PageQuery pageQuery, HttpServletResponse response);
    void exportAsync(ProductQueryBo bo, PageQuery pageQuery, HttpServletResponse response);

    void skuShelf(ProductSkuShelfBo bo);
    void manualProductPush(String productCode);

    void synchronizationProductToEs(String productCode);

    void productListExportNew(ProductQueryBo bo, HttpServletResponse response);

    R productRelatedDataCopy(ProductRelatedDataCopyVo productRelatedDataCopyVo);
}
