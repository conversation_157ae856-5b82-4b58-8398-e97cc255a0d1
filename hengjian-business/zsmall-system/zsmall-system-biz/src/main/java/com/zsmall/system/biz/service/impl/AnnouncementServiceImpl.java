package com.zsmall.system.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.ServiceException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.mapper.SysTenantMapper;
import com.hengjian.system.service.ISysOssService;
import com.zsmall.system.biz.service.IAnnouncementService;
import com.zsmall.system.entity.domain.Announcement;
import com.zsmall.system.entity.domain.AnnouncementOss;
import com.zsmall.system.entity.domain.AnnouncementRead;
import com.zsmall.system.entity.domain.bo.announcement.AnnouncementBo;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementMessageVo;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementVo;
import com.zsmall.system.entity.mapper.AnnouncementMapper;
import com.zsmall.system.entity.mapper.AnnouncementOssMapper;
import com.zsmall.system.entity.mapper.AnnouncementReadMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公告Service业务层处理
 *
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AnnouncementServiceImpl implements IAnnouncementService {

    private final AnnouncementMapper announcementMapper;
    private final AnnouncementOssMapper announcementOssMapper;
    private final AnnouncementReadMapper announcementReadMapper;
    private final ISysOssService sysOssService;
    private final SysTenantMapper sysTenantMapper;
    private final RabbitTemplate rabbitTemplate;

    @Override
    public AnnouncementVo queryById(Long id) {
        AnnouncementVo vo = announcementMapper.selectAnnouncementWithAttachments(id);
        if (ObjectUtil.isNotNull(vo)) {
            // 查询附件信息
            List<AnnouncementOss> ossList = announcementOssMapper.selectByAnnouncementId(id);
            if (CollUtil.isNotEmpty(ossList)) {
                List<Long> ossIds = ossList.stream().map(AnnouncementOss::getOssId).collect(Collectors.toList());
                List<SysOssVo> attachments = sysOssService.listByIds(ossIds);
                vo.setAttachments(attachments);
            }
        }
        return vo;
    }

    @Override
    public TableDataInfo<AnnouncementVo> queryPageList(AnnouncementBo bo, PageQuery pageQuery) {
        String tenantId = null;

        // 如果指定了租户类型（非管理员查询），则只显示有效的公告
        if (ObjectUtil.notEqual(LoginHelper.getTenantType(), TenantType.Manager.name())) {
            // 只显示启用的公告
            bo.setStatus(0);
            // 只显示未过期的公告
            bo.setIsExpired(0);
            tenantId = LoginHelper.getTenantId();
        }
       IPage<AnnouncementVo> page = announcementMapper.queryPageList(pageQuery.build(), bo, tenantId);
        return TableDataInfo.build(page);
    }

    @Override
    public Boolean insertByBo(AnnouncementBo bo) {
        // 权限验证
        validateAdminLogin();
        if (ObjectUtil.notEqual(TenantType.Manager.name(), LoginHelper.getTenantType())) {
            throw new ServiceException("非管理员账号不能创建公告");
        }

        Announcement announcement = MapstructUtils.convert(bo, Announcement.class);
        announcement.setStatus(0);
        announcement.setIsExpired(0);

        boolean result = announcementMapper.insert(announcement) > 0;
        if (result) {
            // 保存附件关联
            saveAnnouncementOss(announcement.getId(), bo.getOssIds());

            // 初始化租户关联记录
            initAnnouncementTenantRecords(announcement.getId(), bo.getTenantType());

            // 发送延迟消息处理过期
            sendDelayedExpireMessage(announcement.getId(), bo.getExpiredTime());

            log.info("创建公告成功，ID: {}, 标题: {}", announcement.getId(), announcement.getTitle());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(AnnouncementBo bo) {
        // 权限验证
        validateAdminLogin();

        Announcement announcement = MapstructUtils.convert(bo, Announcement.class);
        boolean result = announcementMapper.updateById(announcement) > 0;

        if (result) {
            // 更新附件关联
            announcementOssMapper.deleteByAnnouncementId(bo.getId());
            saveAnnouncementOss(bo.getId(), bo.getOssIds());

            // 重新发送延迟消息（如果过期时间有变化）
            if (ObjectUtil.isNotNull(bo.getExpiredTime())) {
                sendDelayedExpireMessage(bo.getId(), bo.getExpiredTime());
            }

            log.info("更新公告成功，ID: {}, 标题: {}", bo.getId(), bo.getTitle());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        // 权限验证
        validateAdminLogin();

        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        // 逻辑删除公告
        List<Announcement> announcements = ids.stream().map(id -> {
            Announcement announcement = new Announcement();
            announcement.setId(id);
            announcement.setDelFlag(1);
            return announcement;
        }).collect(Collectors.toList());

        boolean result = announcementMapper.updateBatchById(announcements);

        if (result) {
            // 删除附件关联和已读记录
            for (Long id : ids) {
                announcementOssMapper.deleteByAnnouncementId(id);
                announcementReadMapper.deleteByAnnouncementId(id);
            }
            log.info("删除公告成功，IDs: {}", ids);
        }
        return result;
    }

    @Override
    public Boolean updateStatus(Long id, Integer status) {
        // 权限验证
        validateAdminLogin();

        Announcement announcement = new Announcement();
        announcement.setId(id);
        announcement.setStatus(status);

        boolean result = announcementMapper.updateById(announcement) > 0;
        if (result) {
            log.info("更新公告状态成功，ID: {}, 状态: {}", id, status);
        }
        return result;
    }



    @Override
    public TableDataInfo<AnnouncementMessageVo> queryMessageList(String tenantId, String tenantType, Integer isRead, PageQuery pageQuery) {
        IPage<AnnouncementMessageVo> page = announcementMapper.queryMessageList(pageQuery.build(), tenantId, tenantType, isRead);
        return TableDataInfo.build(page);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteForTenant(Collection<Long> ids, String tenantId) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        // 租户级删除：通过在announcement_read表中标记来实现，仅对当前租户不可见
        for (Long id : ids) {
            AnnouncementRead readRecord = announcementReadMapper.selectByAnnouncementAndTenant(id, tenantId);

            if (ObjectUtil.isNull(readRecord)) {
                // 如果记录不存在，创建一个标记为删除的记录
                readRecord = new AnnouncementRead();
                readRecord.setAnnouncementId(id);
                readRecord.setTenantId(tenantId);
                readRecord.setIsRead(1); // 标记为已读
                readRecord.setIsShowWindow(1); // 标记为已弹窗
                readRecord.setDelFlag(1); // 标记为已删除
                announcementReadMapper.insert(readRecord);
            } else {
                // 如果记录已存在，更新删除标记
                readRecord.setDelFlag(1);
                announcementReadMapper.updateById(readRecord);
            }
        }

        log.info("租户删除公告成功，租户: {}, 公告IDs: {}", tenantId, ids);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchMarkRead(List<Long> announcementIds, String tenantId) {
        if (CollUtil.isEmpty(announcementIds)) {
            return false;
        }

        // 尝试批量更新已读状态
        int result = announcementReadMapper.batchUpdateReadStatus(announcementIds, tenantId, 1);

        // 如果更新的记录数少于预期，说明可能有新增租户没有历史公告的关联记录
        if (result < announcementIds.size()) {
            log.info("检测到可能存在缺失的公告关联记录，租户: {}, 预期更新: {}, 实际更新: {}",
                tenantId, announcementIds.size(), result);

            // 为缺失的记录创建关联并标记为已读
            createMissingReadRecords(announcementIds, tenantId, 1);
        }

        log.info("批量标记已读成功，租户: {}, 公告IDs: {}, 更新记录数: {}", tenantId, announcementIds, result);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchMarkUnread(List<Long> announcementIds, String tenantId) {
        if (CollUtil.isEmpty(announcementIds)) {
            return false;
        }

        // 尝试批量更新未读状态
        int result = announcementReadMapper.batchUpdateReadStatus(announcementIds, tenantId, 0);

        // 如果更新的记录数少于预期，说明可能有新增租户没有历史公告的关联记录
        if (result < announcementIds.size()) {
            log.info("检测到可能存在缺失的公告关联记录，租户: {}, 预期更新: {}, 实际更新: {}",
                tenantId, announcementIds.size(), result);

            // 为缺失的记录创建关联并标记为未读
            createMissingReadRecords(announcementIds, tenantId, 0);
        }

        log.info("批量标记未读成功，租户: {}, 公告IDs: {}, 更新记录数: {}", tenantId, announcementIds, result);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleAnnouncementExpire(Long announcementId) {
        try {
            // 先查询公告当前状态，验证是否真的需要过期
            Announcement currentAnnouncement = announcementMapper.selectById(announcementId);
            if (ObjectUtil.isNull(currentAnnouncement)) {
                log.warn("公告不存在，无法处理过期，ID: {}", announcementId);
                return;
            }

            // 检查公告是否已经被删除
            if (currentAnnouncement.getDelFlag() == 1) {
                log.info("公告已被删除，无需处理过期，ID: {}", announcementId);
                return;
            }

            // 检查公告是否已经过期
            if (currentAnnouncement.getIsExpired() == 1) {
                log.info("公告已经是过期状态，无需重复处理，ID: {}", announcementId);
                return;
            }

            // 验证当前时间是否确实超过了过期时间（防止延迟消息时间不准确）
            Date expiredTime = currentAnnouncement.getExpiredTime();
            Date currentTime = new Date();
            if (expiredTime.after(currentTime)) {
                log.warn("公告尚未到过期时间，可能是旧的延迟消息，ID: {}, 过期时间: {}, 当前时间: {}",
                    announcementId, DateUtil.formatDateTime(expiredTime), DateUtil.formatDateTime(currentTime));
                return;
            }

            // 更新公告为过期状态
            Announcement updateAnnouncement = new Announcement();
            updateAnnouncement.setId(announcementId);
            updateAnnouncement.setIsExpired(1);

            int result = announcementMapper.updateById(updateAnnouncement);
            if (result > 0) {
                log.info("公告自动过期处理成功，ID: {}, 过期时间: {}", announcementId, DateUtil.formatDateTime(expiredTime));
            } else {
                log.warn("公告自动过期处理失败，可能被并发修改，ID: {}", announcementId);
            }
        } catch (Exception e) {
            log.error("处理公告过期失败，ID: {}", announcementId, e);
            throw e;
        }
    }

    /**
     * 验证管理员权限
     */
    private void validateAdminLogin() {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new ServiceException("请登录后使用");
        }

    }

    /**
     * 保存公告附件关联
     */
    private void saveAnnouncementOss(Long announcementId, List<Long> ossIds) {
        if (CollUtil.isNotEmpty(ossIds)) {
            List<AnnouncementOss> ossList = ossIds.stream().map(ossId -> {
                AnnouncementOss oss = new AnnouncementOss();
                // 由于使用自定义批量插入，需要手动生成ID
                oss.setAnnouncementId(announcementId);
                oss.setOssId(ossId);
                return oss;
            }).collect(Collectors.toList());

            announcementOssMapper.insertBatch(ossList);
        }
    }

    /**
     * 发送延迟过期消息
     */
    private void sendDelayedExpireMessage(Long announcementId, Date expiredTime) {
        try {
            long currentTime = System.currentTimeMillis();
            long expireTime = expiredTime.getTime();
            long delayTime = expireTime - currentTime;

            if (delayTime > 0) {
                // 发送延迟消息，只需要发送公告ID
                rabbitTemplate.convertAndSend(
                    RabbitMqConstant.ACTIVITY_EXPIRE_DELAY_EXCHANGE,
                    RabbitMqConstant.ANNOUNCEMENT_EXPIRE_ROUTING_KEY,
                    announcementId.toString(),
                    message -> {
                        message.getMessageProperties().setHeader("x-delay", delayTime);
                        return message;
                    }
                );

                log.info("发送公告过期延迟消息成功，ID: {}, 过期时间: {}, 延迟: {}ms",
                    announcementId, DateUtil.formatDateTime(expiredTime), delayTime);
            }
        } catch (Exception e) {
            log.error("发送公告过期延迟消息失败，ID: {}", announcementId, e);
        }
    }

    /**
     * 初始化公告租户关联记录
     * 根据公告的租户类型，为所有对应类型的租户创建announcement_read记录
     */
    private void initAnnouncementTenantRecords(Long announcementId, String tenantType) {
        try {
            // 根据租户类型获取所有对应的租户列表
            List<String> tenantIds = getTenantIdsByType(tenantType);

            if (CollUtil.isNotEmpty(tenantIds)) {
                List<AnnouncementRead> readRecords = tenantIds.stream().map(tenantId -> {
                    AnnouncementRead record = new AnnouncementRead();
                    record.setAnnouncementId(announcementId);
                    record.setTenantId(tenantId);
                    record.setIsRead(0); // 默认未读
                    record.setIsShowWindow(0); // 默认未弹窗
                    record.setDelFlag(0); // 默认未删除
                    return record;
                }).collect(Collectors.toList());

                // 批量插入租户关联记录
                announcementReadMapper.insertBatch(readRecords);

                log.info("初始化公告租户关联记录成功，公告ID: {}, 租户类型: {}, 租户数量: {}",
                    announcementId, tenantType, tenantIds.size());
            }
        } catch (Exception e) {
            log.error("初始化公告租户关联记录失败，公告ID: {}, 租户类型: {}", announcementId, tenantType, e);
            // 这里不抛出异常，避免影响公告创建的主流程
        }
    }

    /**
     * 根据租户类型获取租户ID列表
     */
    private List<String> getTenantIdsByType(String tenantType) {
        try {
            if ("Distributor".equals(tenantType)) {
                // 获取所有分销商租户
                // 这里需要调用分销商服务获取租户列表，暂时使用SysTenantService
                LambdaQueryWrapper<SysTenant> q = new LambdaQueryWrapper<>();
                q.eq(SysTenant::getTenantType,tenantType);
                q.eq(SysTenant::getStatus,"0");
                return TenantHelper.ignore(()->sysTenantMapper.selectList(q).stream()
                                                              .map(SysTenant::getTenantId)
                                                              .collect(Collectors.toList()));
            } else if ("Supplier".equals(tenantType)) {
                // 获取所有供应商租户
                LambdaQueryWrapper<SysTenant> q = new LambdaQueryWrapper<>();
                q.eq(SysTenant::getTenantType,tenantType);
                q.eq(SysTenant::getStatus,"0");
                return TenantHelper.ignore(()->sysTenantMapper.selectList(q).stream()
                    .map(SysTenant::getTenantId)
                    .collect(Collectors.toList()));
            }else {
                //查询所有租户
                LambdaQueryWrapper<SysTenant> q = new LambdaQueryWrapper<>();
                q.eq(SysTenant::getStatus,"0");
                return TenantHelper.ignore(()->sysTenantMapper.selectList(q).stream()
                    .map(SysTenant::getTenantId)
                    .collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("获取租户列表失败，租户类型: {}", tenantType, e);
        }
        return new ArrayList<>();
    }

    /**
     * 为缺失的公告关联记录创建记录
     * 处理新增租户可能没有历史公告关联记录的情况
     */
    private void createMissingReadRecords(List<Long> announcementIds, String tenantId, Integer isRead) {
        try {
            // 查询哪些公告ID没有对应的关联记录
            List<Long> missingIds = new ArrayList<>();
            for (Long announcementId : announcementIds) {
                AnnouncementRead existingRecord = announcementReadMapper.selectByAnnouncementAndTenant(announcementId, tenantId);
                if (ObjectUtil.isNull(existingRecord)) {
                    missingIds.add(announcementId);
                }
            }

            if (CollUtil.isNotEmpty(missingIds)) {
                // 为缺失的记录创建关联
                List<AnnouncementRead> readRecords = missingIds.stream().map(id -> {
                    AnnouncementRead record = new AnnouncementRead();
                    record.setAnnouncementId(id);
                    record.setTenantId(tenantId);
                    record.setIsRead(isRead);
                    record.setIsShowWindow(0); // 默认未弹窗
                    record.setDelFlag(0); // 默认未删除
                    return record;
                }).collect(Collectors.toList());

                announcementReadMapper.batchInsert(readRecords);
                log.info("为新增租户创建缺失的公告关联记录成功，租户: {}, 公告IDs: {}, 已读状态: {}",
                    tenantId, missingIds, isRead);
            }
        } catch (Exception e) {
            log.error("创建缺失的公告关联记录失败，租户: {}, 公告IDs: {}", tenantId, announcementIds, e);
            // 不抛出异常，避免影响主流程
        }
    }

}
