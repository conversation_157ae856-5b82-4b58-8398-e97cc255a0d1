package com.zsmall.system.entity.domain.vo.announcement;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.system.entity.domain.AnnouncementOss;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;

/**
 * 公告附件视图对象 announcement_oss
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AnnouncementOss.class)
public class AnnouncementOssVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 公告id
     */
    @ExcelProperty(value = "公告ID")
    private Long announcementId;

    /**
     * 文件ID
     */
    @ExcelProperty(value = "文件ID")
    private Long ossId;

}
