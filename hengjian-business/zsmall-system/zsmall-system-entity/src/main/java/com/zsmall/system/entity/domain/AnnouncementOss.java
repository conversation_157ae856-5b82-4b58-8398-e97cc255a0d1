package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 公告附件表对象 announcement_oss
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@TableName(value = "announcement_oss", autoResultMap = true)
public class AnnouncementOss implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公告id
     */
    private Long announcementId;

    /**
     * 文件ID
     */
    private Long ossId;

    /**
     * 是否删除 0有效 1删除
     */
    @TableLogic
    private Integer delFlag;

}
