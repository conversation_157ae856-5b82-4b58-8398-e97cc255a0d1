package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 公告对象 announcement
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "announcement", autoResultMap = true)
public class Announcement extends NoDeptBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 适用对象（租户类型）
     */
    private String tenantType;

    /**
     * 状态 启用/禁用 0启用 1禁用
     */
    private Integer status;

    /**
     * 过期时间
     */
    private Date expiredTime;

    /**
     * 是否过期 0未过期 1已经过期
     */
    private Integer isExpired;

    /**
     * 富文本内容
     */
    private String content;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer delFlag;

}
