package com.zsmall.system.entity.domain.vo.announcement;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公告消息视图对象（用于消息列表显示）
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@ExcelIgnoreUnannotated
public class AnnouncementMessageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 是否已读 0未读 1已读
     */
    @ExcelProperty(value = "是否已读")
    private Integer isRead;

    /**
     * 是否弹窗(只弹一次) 0未弹窗 1已弹窗
     */
    @ExcelProperty(value = "是否弹窗")
    private Integer isShowWindow;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
