package com.zsmall.system.entity.domain.bo.announcement;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.Announcement;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 公告业务对象 announcement
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Announcement.class, reverseConvertGenerate = false)
public class AnnouncementBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "公告标题不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "公告标题不能超过50个字符")
    private String title;

    /**
     * 适用对象（租户类型）
     */
    @NotBlank(message = "适用对象不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantType;

    /**
     * 状态 启用/禁用 0启用 1禁用
     */
    private Integer status;

    /**
     * 过期时间
     */
    @NotNull(message = "过期时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date expiredTime;

    /**
     * 是否过期 0未过期 1已经过期
     */
    private Integer isExpired;

    /**
     * 富文本内容
     */
    @NotBlank(message = "公告内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 附件OSS ID列表（用于接收前端传递的附件）
     */
    private List<Long> ossIds;

}
