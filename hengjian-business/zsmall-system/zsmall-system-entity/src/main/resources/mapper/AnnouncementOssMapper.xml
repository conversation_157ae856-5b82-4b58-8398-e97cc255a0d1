<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.AnnouncementOssMapper">

    <!-- 根据公告ID查询附件列表 -->
    <select id="selectByAnnouncementId" resultType="com.zsmall.system.entity.domain.AnnouncementOss">
        SELECT
            id,
            announcement_id,
            oss_id
        FROM announcement_oss
        WHERE announcement_id = #{announcementId} AND del_flag = 0
        ORDER BY id ASC
    </select>

    <!-- 根据公告ID删除附件关联（逻辑删除） -->
    <update id="deleteByAnnouncementId">
        UPDATE announcement_oss
        SET del_flag = 1
        WHERE announcement_id = #{announcementId}
    </update>


</mapper>
